package com.opms.data.repository;

import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProductListResponse;
import com.opms.data.model.response.ProductResponse;

import retrofit2.Call;

/**
 * 产品数据仓库接口
 */
public interface ProductRepository {

    /**
     * 获取产品列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 搜索关键词
     * @return 产品列表响应
     */
    Call<ApiResponse<ProductListResponse>> getProductList(int page, int size, String keyword);

    /**
     * 获取产品详情
     *
     * @param id 产品ID
     * @return 产品详情响应
     */
    Call<ApiResponse<ProductResponse>> getProductDetail(int id);

    /**
     * 新增产品
     *
     * @param request 产品请求
     * @return 产品响应
     */
    Call<ApiResponse<ProductResponse>> createProduct(ProductRequest request);

    /**
     * 修改产品
     *
     * @param id      产品ID
     * @param request 产品请求
     * @return 产品响应
     */
    Call<ApiResponse<ProductResponse>> updateProduct(int id, ProductRequest request);

    /**
     * 删除产品
     *
     * @param request 产品请求
     * @return 删除响应
     */
    Call<ApiResponse<Void>> deleteProduct(ProductRequest request);
}