package com.opms.ui.business.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.opms.R;
import com.opms.data.model.response.ComponentResponse;

import java.util.ArrayList;
import java.util.List;

public class ComponentAdapter extends RecyclerView.Adapter<ComponentAdapter.ComponentViewHolder> {

    private Context context;
    private List<ComponentResponse> components;
    private OnComponentClickListener listener;

    public ComponentAdapter(Context context) {
        this.context = context;
        this.components = new ArrayList<>();
    }

    public void setComponents(List<ComponentResponse> components) {
        this.components = components != null ? components : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void setOnComponentClickListener(OnComponentClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ComponentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_component, parent, false);
        return new ComponentViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ComponentViewHolder holder, int position) {
        ComponentResponse component = components.get(position);
        holder.bind(component);
    }

    @Override
    public int getItemCount() {
        return components.size();
    }

    public interface OnComponentClickListener {
        void onComponentDelete(ComponentResponse component);

        void onComponentEditInNewPage(ComponentResponse component);
    }

    public class ComponentViewHolder extends RecyclerView.ViewHolder {
        private com.google.android.material.card.MaterialCardView cardComponent;
        private ImageView ivComponentImage;
        private TextView tvComponentName;
        private TextView tvComponentCode;
        private TextView tvComponentModel;
        private TextView tvComponentStandard;
        private TextView tvComponentRemark;
        private com.google.android.material.button.MaterialButton btnEdit;
        private com.google.android.material.button.MaterialButton btnDelete;

        public ComponentViewHolder(@NonNull View itemView) {
            super(itemView);
            cardComponent = itemView.findViewById(R.id.card_component);
            ivComponentImage = itemView.findViewById(R.id.iv_component_image);
            tvComponentName = itemView.findViewById(R.id.tv_component_name);
            tvComponentCode = itemView.findViewById(R.id.tv_component_code);
            tvComponentModel = itemView.findViewById(R.id.tv_component_model);
            tvComponentStandard = itemView.findViewById(R.id.tv_component_standard);
            tvComponentRemark = itemView.findViewById(R.id.tv_component_remark);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(ComponentResponse component) {
            // 部件图片
            if (component.getImage() != null && !component.getImage().isEmpty()) {
                Glide.with(context)
                        .load(component.getImage())
                        .placeholder(R.drawable.ic_component_management)
                        .error(R.drawable.ic_component_management)
                        .circleCrop()
                        .into(ivComponentImage);
            } else {
                ivComponentImage.setImageResource(R.drawable.ic_component_management);
            }

            // 部件信息
            tvComponentName.setText(component.getName());
            tvComponentCode.setText("编码: " + component.getCode());
            tvComponentModel.setText("型号: " + (component.getModel() != null ? component.getModel() : ""));
            tvComponentStandard.setText("规格: " + (component.getStandard() != null ? component.getStandard() : ""));
            tvComponentRemark.setText("备注: " + (component.getRemark() != null ? component.getRemark() : ""));

            // 点击事件
            // 点击卡片进入编辑状态
            cardComponent.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onComponentEditInNewPage(component);
                }
            });

            // 编辑按钮点击事件
            btnEdit.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onComponentEditInNewPage(component);
                }
            });

            // 删除按钮点击事件
            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onComponentDelete(component);
                }
            });
        }
    }
}
