package com.opms.di.module;

import android.app.Application;
import android.content.Context;

import com.opms.data.local.AppDatabase;
import com.opms.data.local.PreferencesManager;
import com.opms.data.local.dao.CustomerDao;
import com.opms.data.local.dao.DepartmentDao;
import com.opms.data.local.dao.OrderDao;
import com.opms.data.local.dao.OrderItemDao;
import com.opms.data.local.dao.OrderProcessDao;
import com.opms.data.local.dao.PositionDao;
import com.opms.data.local.dao.PostDao;
import com.opms.data.local.dao.ProductDao;
import com.opms.data.local.dao.UserDao;
import com.opms.data.remote.ApiService;
import com.opms.data.repository.OrderRepository;
import com.opms.data.repository.OrderRepositoryImpl;
import com.opms.data.repository.ProductRepository;
import com.opms.data.repository.ProductRepositoryImpl;

import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public class AppModule {
    @Provides
    @Singleton
    public AppDatabase provideAppDatabase(@ApplicationContext Context context) {
        return AppDatabase.getInstance(context);
    }

    @Provides
    @Singleton
    public PreferencesManager providePreferencesManager(@ApplicationContext Context context) {
        return new PreferencesManager(context);
    }

    @Provides
    @Singleton
    public UserDao provideUserDao(AppDatabase database) {
        return database.userDao();
    }

    @Provides
    @Singleton
    public DepartmentDao provideDepartmentDao(AppDatabase database) {
        return database.departmentDao();
    }



    @Provides
    @Singleton
    public PositionDao providePositionDao(AppDatabase database) {
        return database.positionDao();
    }


    @Provides
    @Singleton
    public PostDao providePostDao(AppDatabase database) {
        return database.postDao();
    }


    @Provides
    @Singleton
    public ProductDao provideProductDao(AppDatabase database) {
        return database.productDao();
    }

    @Provides
    @Singleton
    public CustomerDao provideCustomerDao(AppDatabase database) {
        return database.customerDao();
    }


    @Provides
    @Singleton
    public OrderDao provideOrderDao(AppDatabase database) {
        return database.orderDao();
    }

    @Provides
    @Singleton
    public OrderItemDao provideOrderItemDao(AppDatabase database) {
        return database.orderItemDao();
    }

    @Provides
    @Singleton
    public OrderProcessDao provideOrderProcessDao(AppDatabase database) {
        return database.orderProcessDao();
    }

    @Provides
    @Singleton
    public OrderRepository provideOrderRepository(OrderDao orderDao, OrderItemDao orderItemDao, OrderProcessDao orderProcessDao, ApiService apiService) {
        return new OrderRepositoryImpl(orderDao,apiService);
    }

    @Provides
    @Singleton
    Context provideContext(Application application) {
        return application;
    }
}