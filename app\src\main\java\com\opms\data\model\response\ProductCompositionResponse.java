package com.opms.data.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 产品构成信息Model
 */
public class ProductCompositionResponse {
    /**
     * Id
     */
    @SerializedName("id")
    private int id;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    /**
     * 产品Id
     */
    @SerializedName("productId")
    private int productId;

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    /**
     * 部件Id
     */
    @SerializedName("component")
    private ComponentResponse component;

    public ComponentResponse getComponent() {
        return component;
    }

    public void setComponent(ComponentResponse component) {
        this.component = component;
    }

    /**
     * 部件数量
     */
    @SerializedName("number")
    private int number;

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    /**
     * 状态
     */
    @SerializedName("status")
    private String status;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
