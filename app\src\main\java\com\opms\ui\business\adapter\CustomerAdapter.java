package com.opms.ui.business.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.opms.R;
import com.opms.data.model.response.CustomerResponse;

import java.util.ArrayList;
import java.util.List;

public class CustomerAdapter extends RecyclerView.Adapter<CustomerAdapter.CustomerViewHolder> {

    private Context context;
    private List<CustomerResponse> customers;
    private OnCustomerClickListener listener;

    public CustomerAdapter(Context context) {
        this.context = context;
        this.customers = new ArrayList<>();
    }

    public void setCustomers(List<CustomerResponse> customers) {
        this.customers = customers != null ? customers : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void setOnCustomerClickListener(OnCustomerClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public CustomerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_customer, parent, false);
        return new CustomerViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CustomerViewHolder holder, int position) {
        CustomerResponse customer = customers.get(position);
        holder.bind(customer);
    }

    @Override
    public int getItemCount() {
        return customers.size();
    }

    public interface OnCustomerClickListener {
        void onCustomerDelete(CustomerResponse customer);

        void onCustomerEditInNewPage(CustomerResponse customer);
    }

    class CustomerViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private ImageView ivCustomerImage;
        private TextView tvCustomerName;
        private TextView tvCustomerCode;
        private TextView tvCompanyName;
        private TextView tvContact;
        private TextView tvPhone;
        private TextView tvAddress;
        private TextView tvStatus;
        private MaterialButton btnEdit;
        private MaterialButton btnDelete;

        public CustomerViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_customer);
            ivCustomerImage = itemView.findViewById(R.id.iv_customer_image);
            tvCustomerName = itemView.findViewById(R.id.tv_customer_name);
            tvCustomerCode = itemView.findViewById(R.id.tv_customer_code);
            tvCompanyName = itemView.findViewById(R.id.tv_company_name);
            tvContact = itemView.findViewById(R.id.tv_contact);
            tvPhone = itemView.findViewById(R.id.tv_phone);
            tvAddress = itemView.findViewById(R.id.tv_address);
            tvStatus = itemView.findViewById(R.id.tv_status);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(CustomerResponse customer) {
            // 客户图片
            if (customer.getImage() != null && !customer.getImage().isEmpty()) {
                Glide.with(context)
                        .load(customer.getImage())
                        .placeholder(R.drawable.ic_customer_management)
                        .error(R.drawable.ic_customer_management)
                        .circleCrop()
                        .into(ivCustomerImage);
            } else {
                ivCustomerImage.setImageResource(R.drawable.ic_customer_management);
            }

            // 客户名称
            tvCustomerName.setText(customer.getName() != null ? customer.getName() : "未知客户");

            // 客户编码
            tvCustomerCode.setText("编码: " + (customer.getCode() != null ? customer.getCode() : "无"));

            // 公司名称
            tvCompanyName.setText("公司: " + (customer.getCompanyName() != null ? customer.getCompanyName() : "无"));

            // 联系人
            tvContact.setText("联系人: " + (customer.getContact() != null ? customer.getContact() : "无"));

            // 联系电话
            tvPhone.setText("电话: " + (customer.getPhone() != null ? customer.getPhone() : "无"));

            // 地址
            String address = customer.getAddress() != null ? customer.getAddress() : "无";
            if (address.length() > 30) {
                address = address.substring(0, 30) + "...";
            }
            tvAddress.setText("地址: " + address);

            // 状态
            String status = customer.getStatus();
            if ("1".equals(status)) {
                tvStatus.setText("启用");
                tvStatus.setTextColor(context.getColor(R.color.success));
                tvStatus.setBackgroundResource(R.drawable.bg_status_active);
            } else if ("0".equals(status)) {
                tvStatus.setText("禁用");
                tvStatus.setTextColor(context.getColor(R.color.error));
                tvStatus.setBackgroundResource(R.drawable.bg_status_inactive);
            } else {
                tvStatus.setText("未知");
                tvStatus.setTextColor(context.getColor(R.color.text_secondary));
                tvStatus.setBackgroundResource(R.drawable.bg_status_unknown);
            }

            // 点击事件
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCustomerEditInNewPage(customer);
                }
            });

            btnEdit.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCustomerEditInNewPage(customer);
                }
            });

            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCustomerDelete(customer);
                }
            });
        }
    }
}
