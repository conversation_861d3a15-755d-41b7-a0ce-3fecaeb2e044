package com.opms.ui.business;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProductResponse;
import com.opms.data.repository.ProductRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.databinding.ActivityProductEditBinding;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ProductEditActivity extends AppCompatActivity {

    private static final String TAG = "ProductEditActivity";

    @Inject
    ProductRepository productRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityProductEditBinding binding;
    private int productId = -1;
    private boolean isEditMode = false;
    private Uri selectedImageUri;
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProductEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getIntentData();
        setupImagePicker();
        setupToolbar();
        setupStatusSpinner();
        setupButtons();
        setupImageView();

        if (isEditMode) {
            loadProductDetail();
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("product_id")) {
            productId = intent.getIntExtra("product_id", -1);
            isEditMode = productId != -1;
        }
    }

    private void setupImagePicker() {
        Log.d(TAG, "setupImagePicker: 初始化图片选择器");

        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    Log.d(TAG, "权限请求结果: " + isGranted);
                    if (isGranted) {
                        launchImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.d(TAG, "图片选择器返回结果: " + result.getResultCode());
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        selectedImageUri = result.getData().getData();
                        Log.d(TAG, "选中的图片URI: " + selectedImageUri);
                        if (selectedImageUri != null) {
                            // 显示选中的图片
                            ImageUtils.loadUserAvatar(this, selectedImageUri, binding.ivProductImage);
                            // 如果是编辑模式，立即上传图片
                            if (isEditMode) {
                                Log.d(TAG, "编辑模式，开始上传图片");
                                uploadProductImage();
                            } else {
                                Log.d(TAG, "新增模式，不上传图片");
                            }
                        } else {
                            Log.w(TAG, "选中的图片URI为空");
                        }
                    } else {
                        Log.w(TAG, "图片选择被取消或失败");
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "编辑产品" : "新增产品");
        }
    }

    private void setupStatusSpinner() {
        String[] statusOptions = {"启用", "禁用"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_dropdown_item_1line, statusOptions);
        binding.spinnerStatus.setAdapter(adapter);
        binding.spinnerStatus.setText("启用", false);
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveProduct());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupImageView() {
        Log.d(TAG, "setupImageView: 设置图片视图，编辑模式: " + isEditMode);
        // 只有在编辑模式下才允许点击图片
        if (isEditMode) {
            // 设置覆盖层的点击事件
            binding.viewImageOverlay.setOnClickListener(v -> {
                Log.d(TAG, "图片覆盖层被点击，打开图片选择器");
                openImagePicker();
            });
            binding.viewImageOverlay.setClickable(true);
            binding.viewImageOverlay.setFocusable(true);
            binding.tvImageHint.setVisibility(View.VISIBLE);

            // 也设置CardView的点击事件作为备用
            binding.cardImage.setOnClickListener(v -> {
                Log.d(TAG, "图片卡片被点击，打开图片选择器");
                openImagePicker();
            });

            Log.d(TAG, "setupImageView: 编辑模式图片点击事件已设置");
        } else {
            // 新增模式下禁用图片点击
            binding.viewImageOverlay.setOnClickListener(null);
            binding.viewImageOverlay.setClickable(false);
            binding.viewImageOverlay.setFocusable(false);
            binding.cardImage.setOnClickListener(null);
            binding.tvImageHint.setVisibility(View.GONE);

            Log.d(TAG, "setupImageView: 新增模式图片点击事件已禁用");
        }
    }

    private void loadProductDetail() {
        showLoading(true);

        Call<ApiResponse<ProductResponse>> call = productRepository.getProductDetail(productId);
        call.enqueue(new Callback<ApiResponse<ProductResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ProductResponse>> call,
                                   Response<ApiResponse<ProductResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProductResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        ProductResponse product = apiResponse.getData();
                        if (product != null) {
                            fillProductData(product);
                        }
                    } else {
                        showError("获取产品详情失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ProductResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void fillProductData(ProductResponse product) {
        binding.etProductName.setText(product.getName());
        binding.etProductCode.setText(product.getCode());
        binding.etProductModel.setText(product.getModel());
        binding.etProductStandard.setText(product.getStandard());
        binding.etProductPrice.setText(String.valueOf(product.getPrice()));
        binding.spinnerStatus.setText(product.getStatus(), false);
        binding.etProductRemark.setText(product.getRemark());

        // 加载产品图片
        if (!TextUtils.isEmpty(product.getImage())) {
            // 如果有图片URL，使用Glide加载
            Glide.with(this)
                    .load(product.getImage())
                    .placeholder(R.drawable.ic_product_management)
                    .error(R.drawable.ic_product_management)
                    .into(binding.ivProductImage);
        } else {
            // 没有图片时显示默认图片
            binding.ivProductImage.setImageResource(R.drawable.ic_product_management);
        }
    }

    private void saveProduct() {
        if (!validateInput()) {
            return;
        }

        ProductRequest request = createProductRequest();
        showLoading(true);

        Call<ApiResponse<ProductResponse>> call;
        if (isEditMode) {
            call = productRepository.updateProduct(productId, request);
        } else {
            call = productRepository.createProduct(request);
        }

        call.enqueue(new Callback<ApiResponse<ProductResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ProductResponse>> call,
                                   Response<ApiResponse<ProductResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ProductResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        String message = isEditMode ? "产品更新成功" : "产品创建成功";
                        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();

                        // 返回结果
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError("保存失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ProductResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        String name = binding.etProductName.getText().toString().trim();
        String code = binding.etProductCode.getText().toString().trim();
        String priceStr = binding.etProductPrice.getText().toString().trim();

        if (TextUtils.isEmpty(name)) {
            binding.etProductName.setError("产品名称不能为空");
            binding.etProductName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(code)) {
            binding.etProductCode.setError("产品编码不能为空");
            binding.etProductCode.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(priceStr)) {
            binding.etProductPrice.setError("价格不能为空");
            binding.etProductPrice.requestFocus();
            return false;
        }

        try {
            double price = Double.parseDouble(priceStr);
            if (price < 0) {
                binding.etProductPrice.setError("价格不能为负数");
                binding.etProductPrice.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            binding.etProductPrice.setError("请输入有效的价格");
            binding.etProductPrice.requestFocus();
            return false;
        }

        return true;
    }

    private ProductRequest createProductRequest() {
        ProductRequest request = new ProductRequest();
        request.setId(productId);
        request.setName(binding.etProductName.getText().toString().trim());
        request.setCode(binding.etProductCode.getText().toString().trim());
        request.setModel(binding.etProductModel.getText().toString().trim());
        request.setStandard(binding.etProductStandard.getText().toString().trim());
        request.setPrice(Double.parseDouble(binding.etProductPrice.getText().toString().trim()));
        request.setStatus(binding.spinnerStatus.getText().toString());
        request.setRemark(binding.etProductRemark.getText().toString().trim());

        // 新增时不设置image字段，编辑时也不在这里设置（单独上传）
        // request.setImage(null);

        return request;
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 开始打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        launchImagePicker();
    }

    private void launchImagePicker() {
        Log.d(TAG, "launchImagePicker: 启动图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            intent.setType("image/*");
            Log.d(TAG, "launchImagePicker: 启动图片选择器Intent");
            imagePickerLauncher.launch(intent);
        } catch (Exception e) {
            Log.e(TAG, "launchImagePicker: 启动图片选择器失败", e);
            showError("无法打开图片选择器: " + e.getMessage());
        }
    }

    private void uploadProductImage() {
        if (selectedImageUri == null || productId == -1) {
            Log.w(TAG, "uploadProductImage: 图片URI或产品ID无效");
            return;
        }

        Log.d(TAG, "uploadProductImage: 开始上传产品图片，产品ID: " + productId);

        // 获取当前操作人（这里可以从用户会话中获取）
        String operator = ImageUploadUtils.getCurrentUser(this);

        // 使用通用图片上传方法
        imageUploadRepository.uploadImage(
                this,
                selectedImageUri,
                ImageUploadUtils.BusinessType.PRODUCT,
                String.valueOf(productId),
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        Log.d(TAG, "产品图片上传开始");
                        showLoading(true);
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        Log.d(TAG, "产品图片上传成功: " + imageUrl);
                        Snackbar.make(binding.getRoot(), "产品图片上传成功", Snackbar.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        Log.e(TAG, "产品图片上传失败: " + errorMessage);
                        showError(errorMessage);
                    }

                    @Override
                    public void onUploadComplete() {
                        Log.d(TAG, "产品图片上传完成");
                        showLoading(false);
                    }
                }
        );
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
