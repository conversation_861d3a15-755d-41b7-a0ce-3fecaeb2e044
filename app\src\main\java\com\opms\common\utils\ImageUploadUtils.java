package com.opms.common.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.opms.data.local.PreferencesManager;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.remote.ApiService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 通用图片上传工具类
 * 支持不同业务模块的图片上传需求
 */
public class ImageUploadUtils {

    private static final String TAG = "ImageUploadUtils";

    /**
     * 上传图片（从URI）
     *
     * @param context      上下文
     * @param apiService   API服务
     * @param imageUri     图片URI
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    public static void uploadImage(Context context,
                                   ApiService apiService,
                                   Uri imageUri,
                                   BusinessType businessType,
                                   String businessId,
                                   String operator,
                                   ImageUploadCallback callback) {

        Log.d(TAG, "开始上传图片: businessType=" + businessType.getValue() +
                ", businessId=" + businessId + ", operator=" + operator);

        if (callback != null) {
            callback.onUploadStart();
        }

        try {
            // 将URI转换为Bitmap
            Bitmap bitmap = MediaStore.Images.Media.getBitmap(context.getContentResolver(), imageUri);

            // 创建临时文件
            File tempFile = createTempImageFile(context, bitmap, businessType, businessId);

            // 上传文件
            uploadImageFile(apiService, tempFile, businessType, businessId, operator, callback);

        } catch (IOException e) {
            Log.e(TAG, "处理图片失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onUploadFailure("处理图片失败: " + e.getMessage());
                callback.onUploadComplete();
            }
        }
    }

    /**
     * 上传图片（从文件）
     *
     * @param apiService   API服务
     * @param imageFile    图片文件
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    public static void uploadImageFile(ApiService apiService,
                                       File imageFile,
                                       BusinessType businessType,
                                       String businessId,
                                       String operator,
                                       ImageUploadCallback callback) {

        Log.d(TAG, "上传图片文件: " + imageFile.getAbsolutePath());

        try {
            // 创建请求参数
            RequestBody businessTypeBody = RequestBody.create(MediaType.parse("text/plain"), businessType.getValue());
            RequestBody businessIdBody = RequestBody.create(MediaType.parse("text/plain"), businessId);
            RequestBody operatorBody = RequestBody.create(MediaType.parse("text/plain"), operator);

            // 创建文件的MultipartBody.Part
            RequestBody requestFile = RequestBody.create(MediaType.parse("image/jpeg"), imageFile);
            MultipartBody.Part filePart = MultipartBody.Part.createFormData("image", imageFile.getName(), requestFile);

            // 调用API
            Call<ApiResponse<String>> call = apiService.uploadImage(businessTypeBody, businessIdBody, operatorBody, filePart);
            call.enqueue(new Callback<ApiResponse<String>>() {
                @Override
                public void onResponse(@NonNull Call<ApiResponse<String>> call,
                                       @NonNull Response<ApiResponse<String>> response) {

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<String> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            Log.d(TAG, "图片上传成功: " + apiResponse.getData());
                            if (callback != null) {
                                callback.onUploadSuccess(apiResponse.getData());
                            }
                        } else {
                            String errorMsg = apiResponse.getMessage() != null ? apiResponse.getMessage() : "图片上传失败";
                            Log.e(TAG, "图片上传失败: " + errorMsg);
                            if (callback != null) {
                                callback.onUploadFailure(errorMsg);
                            }
                        }
                    } else {
                        Log.e(TAG, "图片上传失败: HTTP " + response.code());
                        if (callback != null) {
                            callback.onUploadFailure("图片上传失败");
                        }
                    }

                    // 清理临时文件
                    if (imageFile.exists()) {
                        boolean deleted = imageFile.delete();
                        Log.d(TAG, "临时文件删除: " + deleted);
                    }

                    if (callback != null) {
                        callback.onUploadComplete();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ApiResponse<String>> call, @NonNull Throwable t) {
                    Log.e(TAG, "图片上传网络错误: " + t.getMessage(), t);

                    // 清理临时文件
                    if (imageFile.exists()) {
                        boolean deleted = imageFile.delete();
                        Log.d(TAG, "临时文件删除: " + deleted);
                    }

                    if (callback != null) {
                        callback.onUploadFailure("网络错误: " + t.getMessage());
                        callback.onUploadComplete();
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "创建上传请求失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onUploadFailure("创建上传请求失败: " + e.getMessage());
                callback.onUploadComplete();
            }
        }
    }

    /**
     * 创建临时图片文件
     */
    private static File createTempImageFile(Context context, Bitmap bitmap, BusinessType businessType, String businessId) throws IOException {
        // 创建临时文件目录
        File cacheDir = new File(context.getCacheDir(), "temp_images");
        if (!cacheDir.exists()) {
            boolean created = cacheDir.mkdirs();
            Log.d(TAG, "创建临时目录: " + created);
        }

        // 创建临时文件
        String fileName = businessType.getValue() + "_" + businessId + "_" + System.currentTimeMillis() + ".jpg";
        File imageFile = new File(cacheDir, fileName);

        // 保存Bitmap到文件
        FileOutputStream fos = new FileOutputStream(imageFile);
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
        fos.close();

        Log.d(TAG, "创建临时文件: " + imageFile.getAbsolutePath() + ", 大小: " + imageFile.length() + " bytes");

        return imageFile;
    }

    /**
     * 获取当前用户名
     * 从PreferencesManager中获取已登录用户的用户名
     */
    public static String getCurrentUser(Context context) {
        try {
            PreferencesManager preferencesManager = new PreferencesManager(context);
            String username = preferencesManager.getUsername();

            if (!TextUtils.isEmpty(username)) {
                Log.d(TAG, "获取当前用户: " + username);
                return username;
            } else {
                Log.w(TAG, "用户未登录，使用默认操作人");
                return "system";
            }
        } catch (Exception e) {
            Log.e(TAG, "获取当前用户失败: " + e.getMessage(), e);
            return "system";
        }
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        USER("user"),           // 用户头像
        CUSTOMER("customer"),   // 客户图片
        PRODUCT("product"),     // 产品图片
        ORDER("order"),         // 订单图片
        COMPONENT("component"); // 部件图片

        private final String value;

        BusinessType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 图片上传回调接口
     */
    public interface ImageUploadCallback {
        void onUploadStart();

        void onUploadSuccess(String imageUrl);

        void onUploadFailure(String errorMessage);

        void onUploadComplete();
    }
}
