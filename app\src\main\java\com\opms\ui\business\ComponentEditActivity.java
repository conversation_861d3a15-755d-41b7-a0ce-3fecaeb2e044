package com.opms.ui.business;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.ImageUtils;
import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ComponentResponse;
import com.opms.data.repository.ComponentRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.databinding.ActivityComponentEditBinding;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ComponentEditActivity extends AppCompatActivity {

    private static final String TAG = "ComponentEditActivity";

    @Inject
    ComponentRepository componentRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityComponentEditBinding binding;
    private int componentId = -1;
    private boolean isEditMode = false;
    private Uri selectedImageUri;
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityComponentEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getIntentData();
        setupImagePicker();
        setupToolbar();
        setupButtons();
        setupImageView();

        if (isEditMode) {
            loadComponentDetail();
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("component_id")) {
            componentId = intent.getIntExtra("component_id", -1);
            isEditMode = componentId != -1;
        }
    }

    private void setupImagePicker() {
        Log.d(TAG, "setupImagePicker: 初始化图片选择器");

        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    Log.d(TAG, "权限请求结果: " + isGranted);
                    if (isGranted) {
                        launchImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );

        // 图片选择launcher
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.d(TAG, "图片选择器返回结果: " + result.getResultCode());
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        selectedImageUri = result.getData().getData();
                        Log.d(TAG, "选中的图片URI: " + selectedImageUri);
                        if (selectedImageUri != null) {
                            // 显示选中的图片
                            ImageUtils.loadUserAvatar(this, selectedImageUri, binding.ivComponentImage);
                            // 如果是编辑模式，立即上传图片
                            if (isEditMode) {
                                Log.d(TAG, "编辑模式，开始上传图片");
                                uploadComponentImage();
                            } else {
                                Log.d(TAG, "新增模式，不上传图片");
                            }
                        } else {
                            Log.w(TAG, "选中的图片URI为空");
                        }
                    } else {
                        Log.w(TAG, "图片选择被取消或失败");
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "编辑部件" : "新增部件");
        }
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveComponent());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupImageView() {
        Log.d(TAG, "setupImageView: 设置图片视图，编辑模式: " + isEditMode);
        // 只有在编辑模式下才允许点击图片
        if (isEditMode) {
            // 设置覆盖层的点击事件
            binding.viewImageOverlay.setOnClickListener(v -> {
                Log.d(TAG, "图片覆盖层被点击，打开图片选择器");
                openImagePicker();
            });
            binding.viewImageOverlay.setClickable(true);
            binding.viewImageOverlay.setFocusable(true);
            binding.tvImageHint.setVisibility(View.VISIBLE);

            // 也设置CardView的点击事件作为备用
            binding.cardImage.setOnClickListener(v -> {
                Log.d(TAG, "图片卡片被点击，打开图片选择器");
                openImagePicker();
            });

            Log.d(TAG, "setupImageView: 编辑模式图片点击事件已设置");
        } else {
            // 新增模式下禁用图片点击
            binding.viewImageOverlay.setOnClickListener(null);
            binding.viewImageOverlay.setClickable(false);
            binding.viewImageOverlay.setFocusable(false);
            binding.cardImage.setOnClickListener(null);
            binding.tvImageHint.setVisibility(View.GONE);

            Log.d(TAG, "setupImageView: 新增模式图片点击事件已禁用");
        }
    }

    private void loadComponentDetail() {
        showLoading(true);

        Call<ApiResponse<ComponentResponse>> call = componentRepository.getComponentDetail(componentId);
        call.enqueue(new Callback<ApiResponse<ComponentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentResponse>> call,
                                   Response<ApiResponse<ComponentResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        ComponentResponse component = apiResponse.getData();
                        if (component != null) {
                            fillComponentData(component);
                        }
                    } else {
                        showError("获取部件详情失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void fillComponentData(ComponentResponse component) {
        binding.etComponentName.setText(component.getName());
        binding.etComponentCode.setText(component.getCode());
        binding.etComponentModel.setText(component.getModel());
        binding.etComponentStandard.setText(component.getStandard());
        binding.etComponentRemark.setText(component.getRemark());

        // 加载部件图片
        if (!TextUtils.isEmpty(component.getImage())) {
            // 如果有图片URL，使用Glide加载
            Glide.with(this)
                    .load(component.getImage())
                    .placeholder(R.drawable.ic_component_management)
                    .error(R.drawable.ic_component_management)
                    .into(binding.ivComponentImage);
        } else {
            // 没有图片时显示默认图片
            binding.ivComponentImage.setImageResource(R.drawable.ic_component_management);
        }
    }

    private void saveComponent() {
        if (!validateInput()) {
            return;
        }

        ComponentRequest request = createComponentRequest();
        showLoading(true);

        Call<ApiResponse<ComponentResponse>> call;
        if (isEditMode) {
            call = componentRepository.updateComponent(componentId, request);
        } else {
            call = componentRepository.createComponent(request);
        }

        call.enqueue(new Callback<ApiResponse<ComponentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentResponse>> call,
                                   Response<ApiResponse<ComponentResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        String message = isEditMode ? "部件更新成功" : "部件创建成功";
                        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();

                        // 返回结果
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError("保存失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        String name = binding.etComponentName.getText().toString().trim();
        String code = binding.etComponentCode.getText().toString().trim();

        if (TextUtils.isEmpty(name)) {
            binding.etComponentName.setError("部件名称不能为空");
            binding.etComponentName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(code)) {
            binding.etComponentCode.setError("部件编码不能为空");
            binding.etComponentCode.requestFocus();
            return false;
        }

        return true;
    }

    private ComponentRequest createComponentRequest() {
        ComponentRequest request = new ComponentRequest();
        request.setId(componentId);
        request.setName(binding.etComponentName.getText().toString().trim());
        request.setCode(binding.etComponentCode.getText().toString().trim());
        request.setModel(binding.etComponentModel.getText().toString().trim());
        request.setStandard(binding.etComponentStandard.getText().toString().trim());
        request.setRemark(binding.etComponentRemark.getText().toString().trim());

        // 新增时不设置image字段，编辑时也不在这里设置（单独上传）
        // request.setImage(null);

        return request;
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 开始打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        launchImagePicker();
    }

    private void launchImagePicker() {
        Log.d(TAG, "launchImagePicker: 启动图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            intent.setType("image/*");
            Log.d(TAG, "launchImagePicker: 启动图片选择器Intent");
            imagePickerLauncher.launch(intent);
        } catch (Exception e) {
            Log.e(TAG, "launchImagePicker: 启动图片选择器失败", e);
            showError("无法打开图片选择器: " + e.getMessage());
        }
    }

    private void uploadComponentImage() {
        if (selectedImageUri == null || componentId == -1) {
            Log.w(TAG, "uploadComponentImage: 图片URI或部件ID无效");
            return;
        }

        Log.d(TAG, "uploadComponentImage: 开始上传部件图片，部件ID: " + componentId);

        // 获取当前操作人（这里可以从用户会话中获取）
        String operator = ImageUploadUtils.getCurrentUser(this);

        // 使用通用图片上传方法
        imageUploadRepository.uploadImage(
                this,
                selectedImageUri,
                ImageUploadUtils.BusinessType.COMPONENT,
                String.valueOf(componentId),
                operator,
                new ImageUploadUtils.ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        Log.d(TAG, "部件图片上传开始");
                        showLoading(true);
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        Log.d(TAG, "部件图片上传成功: " + imageUrl);
                        Snackbar.make(binding.getRoot(), "部件图片上传成功", Snackbar.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        Log.e(TAG, "部件图片上传失败: " + errorMessage);
                        showError(errorMessage);
                    }

                    @Override
                    public void onUploadComplete() {
                        Log.d(TAG, "部件图片上传完成");
                        showLoading(false);
                    }
                }
        );
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
